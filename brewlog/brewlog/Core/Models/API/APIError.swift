import Foundation

/// API错误类型，用于表示与API交互过程中可能发生的错误
enum APIError: Error, Equatable {
    /// URL无效
    case invalidURL
    /// 网络错误
    case networkError(Error)
    /// 认证错误，如未登录或令牌过期
    case authenticationError(String)
    /// 授权错误，如无权限访问资源
    case unauthorized
    /// 请求验证错误
    case validationError(String, [String: [String]])
    /// 服务器错误
    case serverError(Int, String) // 带状态码的服务器错误
    /// 解码错误
    case decodingError(Error)
    /// 编码错误
    case encodingError(Error)
    /// 无效的响应
    case invalidResponse
    /// 服务器错误（简化版，只有消息）
    case serverErrorSimple(String)
    /// HTTP错误
    case httpError(Int)
    /// 无效数据
    case invalidData(String)
    /// 未知错误
    case unknownError(String)
    /// 未知错误（无详情）
    case unknown
    /// 速率限制错误（429 Too Many Requests）
    case rateLimited(String)
    
    /// 判断是否为解码错误
    var isDecodingError: Bool {
        if case .decodingError = self {
            return true
        }
        return false
    }
    
    /// 用户友好的错误消息
    var userFriendlyMessage: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .authenticationError(let message):
            return "认证错误: \(message)"
        case .unauthorized:
            return "请先登录"
        case .validationError(let message, _):
            return "表单验证错误，\(message)"
        case .serverError(let code, let message):
            return "服务器错误(\(code)): \(message)"
        case .decodingError:
            return "数据解析错误"
        case .encodingError:
            return "数据编码错误"
        case .invalidResponse:
            return "无效的服务器响应"
        case .serverErrorSimple(let message):
            return "服务器错误: \(message)"
        case .httpError(let code):
            return "HTTP错误: \(code)"
        case .invalidData(let message):
            return "无效数据: \(message)"
        case .unknownError(let message):
            return "未知错误: \(message)"
        case .unknown:
            return "发生未知错误"
        case .rateLimited(let message):
            return "请求过于频繁: \(message)"
        }
    }
    
    /// 错误消息
    var message: String {
        return userFriendlyMessage
    }
    
    /// 错误详情
    var details: [String: [String]]? {
        switch self {
        case .validationError(_, let details):
            return details
        default:
            return nil
        }
    }
    
    // Equatable协议实现
    static func == (lhs: APIError, rhs: APIError) -> Bool {
        switch (lhs, rhs) {
        case (.invalidURL, .invalidURL):
            return true
        case (.networkError(let lhsError), .networkError(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        case (.authenticationError(let lhsMessage), .authenticationError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.unauthorized, .unauthorized):
            return true
        case (.validationError(let lhsMessage, let lhsDetails), .validationError(let rhsMessage, let rhsDetails)):
            return lhsMessage == rhsMessage && lhsDetails == rhsDetails
        case (.serverError(let lhsCode, let lhsMessage), .serverError(let rhsCode, let rhsMessage)):
            return lhsCode == rhsCode && lhsMessage == rhsMessage
        case (.decodingError(let lhsError), .decodingError(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        case (.encodingError(let lhsError), .encodingError(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        case (.invalidResponse, .invalidResponse):
            return true
        case (.serverErrorSimple(let lhsMessage), .serverErrorSimple(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.httpError(let lhsCode), .httpError(let rhsCode)):
            return lhsCode == rhsCode
        case (.invalidData(let lhsMessage), .invalidData(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.unknownError(let lhsMessage), .unknownError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.unknown, .unknown):
            return true
        case (.rateLimited(let lhsMessage), .rateLimited(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// 实现LocalizedError协议
extension APIError: LocalizedError {
    public var errorDescription: String? {
        return userFriendlyMessage
    }
} 