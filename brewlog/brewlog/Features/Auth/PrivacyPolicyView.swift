import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var scrollOffset: CGFloat = 0
    @State private var titleOpacity: Double = 1.0
    @State private var navTitleOpacity: Double = 0.0
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Group {
                        Text("隐私政策")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .padding(.bottom, 8)
                            .opacity(titleOpacity)
                        
                        Text("最后更新日期：2025年6月15日")
                            .font(.subheadline)
                            .foregroundColor(.primaryAccent)
                            .padding(.bottom, 16)
                        
                        Text("咖啡札记（\"我们\"）非常重视您的隐私。本隐私政策旨在向您说明我们如何收集、使用、存储和保护您的个人信息，以及您对这些信息所拥有的权利。请您在使用咖啡札记App（\"咖啡札记\"或\"App\"）前仔细阅读本政策。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("1. 我们收集的信息")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("1.1 您提供的信息\n- 账户信息：注册时提供的用户名、电子邮件地址和密码\n- 个人资料：您选择提供的昵称等信息\n- 内容数据：您在App中创建的咖啡冲煮记录、咖啡豆信息、设备信息等\n\n1.2 自动收集的信息\n- 设备信息：设备型号、操作系统版本、唯一设备标识符等\n- 使用数据：App功能使用情况、访问时间、使用时长等\n- 日志信息：错误报告、性能数据等")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("2. 信息使用")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("我们使用收集到的信息：\n- 提供、维护和改进咖啡札记的功能和服务\n- 处理您的账户注册和认证\n- 响应您的请求和提供客户支持\n- 发送服务通知和更新信息\n- 分析App使用情况，优化用户体验\n- 防止欺诈和滥用行为")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("3. 信息共享")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("除以下情况外，我们不会与第三方共享您的个人信息：\n- 经您明确同意\n- 为完成您请求的服务所必需\n- 遵守法律要求、法院命令或政府机构的合法要求\n- 保护咖啡札记、我们的用户或公众的权利、财产或安全")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("4. 数据安全")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("我们采取合理的技术和组织措施保护您的个人信息，防止未经授权的访问、使用或披露。然而，互联网传输无法保证100%的安全，我们无法保证信息传输的绝对安全。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("5. 数据存储与跨境传输")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("您的个人信息主要存储在中国境内的服务器上。我们不会将您的个人信息传输至境外，除非获得您的明确同意并符合相关法律法规的要求。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("6. 您的权利")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("根据适用法律，您对自己的个人信息拥有以下权利：\n- 访问：您有权获知我们收集和处理的关于您的个人信息\n- 更正：您有权要求更正不准确的个人信息\n- 删除：在特定情况下，您有权要求删除您的个人信息\n- 导出：您可以导出您在App中创建的数据\n\n如需行使上述权利，请通过本政策末尾提供的联系方式与我们联系。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("7. 儿童隐私")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("咖啡札记不面向13岁以下儿童。我们不会故意收集13岁以下儿童的个人信息。如果您是父母或监护人，发现您的孩子向我们提供了个人信息，请联系我们，我们将采取措施删除相关信息。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("8. 政策更新")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("我们可能会不时更新本隐私政策。更新后的政策将在App内发布，并在发布时生效。我们鼓励您定期查阅本政策，了解我们如何保护您的信息。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("9. 联系我们")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("如您对本隐私政策或我们的数据处理实践有任何疑问、意见或投诉，请通过以下方式联系我们：\n电子邮件：<EMAIL>")
                            .padding(.bottom, 16)
                    }
                }
                .padding()
                .background(GeometryReader { geometry in
                    Color.clear.preference(key: ScrollOffsetPreferenceKey.self, 
                                          value: geometry.frame(in: .named("scroll")).origin.y)
                })
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
                
                // 当滚动超过一定距离时，逐渐改变标题透明度
                let threshold: CGFloat = -50
                if value < threshold {
                    let progress = min(1.0, (abs(value) - abs(threshold)) / 30)
                    titleOpacity = 1.0 - progress
                    navTitleOpacity = progress
                } else {
                    titleOpacity = 1.0
                    navTitleOpacity = 0.0
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .presentationDragIndicator(.visible)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("隐私政策")
                        .font(.headline)
                        .opacity(navTitleOpacity)
                }
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Text("关闭")
                            .foregroundColor(.linkText)
                    }
                }
            }
        }
    }
}

#Preview {
    PrivacyPolicyView()
} 