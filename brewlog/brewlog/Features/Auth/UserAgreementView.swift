import SwiftUI

struct UserAgreementView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var scrollOffset: CGFloat = 0
    @State private var titleOpacity: Double = 1.0
    @State private var navTitleOpacity: Double = 0.0
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    Group {
                        Text("用户协议")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .padding(.bottom, 8)
                            .opacity(titleOpacity)
                        
                        Text("最后更新日期：2025年6月15日")
                            .font(.subheadline)
                            .foregroundColor(.primaryAccent)
                            .padding(.bottom, 16)
                        
                        Text("欢迎使用咖啡札记！")
                            .font(.title3)
                            .fontWeight(.semibold)
                        
                        Text("本用户协议（\"协议\"）是您与咖啡札记（以下简称\"我们\"或\"咖啡札记\"）之间就咖啡札记App服务等相关事宜所订立的契约。请您仔细阅读本协议，确保您充分理解本协议中各条款，特别是涉及免除或限制责任的条款。若您不同意本协议的任何内容，请勿注册或使用咖啡札记服务。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("1. 服务内容")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("咖啡札记是一款专为咖啡爱好者设计的应用程序，提供咖啡冲煮记录、咖啡豆管理、设备管理等功能。我们保留随时修改、中断或终止部分或全部服务的权利。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("2. 账户注册与安全")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("2.1 您需要注册账户才能使用咖啡札记的全部功能。注册时，您需要提供真实、准确的个人信息。\n2.2 您应妥善保管账户信息，对您账户下的所有活动负责。如发现任何未经授权使用您账户的情况，请立即通知我们。\n2.3 您的账户仅限您个人使用，不得转让、出售或与他人共享。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("3. 用户行为规范")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("3.1 您承诺遵守中华人民共和国相关法律法规。\n3.2 您不得利用咖啡札记从事任何违法或不当行为，包括但不限于：\n- 发布、传播违法、有害信息\n- 侵犯他人知识产权或其他合法权益\n- 干扰咖啡札记的正常运行\n- 未经授权访问咖啡札记系统或数据")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("4. 知识产权")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("4.1 咖啡札记及其相关内容（包括但不限于文本、图像、代码、界面设计等）的知识产权归我们所有。\n4.2 未经我们明确书面许可，您不得复制、修改、传播或使用咖啡札记的任何内容。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("5. 免责声明")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("5.1 咖啡札记按\"现状\"提供，我们不对服务的适用性、可靠性、准确性、完整性等作任何明示或暗示的保证。\n5.2 我们不对因使用咖啡札记而产生的任何直接或间接损失承担责任，除非这些损失是由我们的故意或重大过失造成的。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("6. 协议修改")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("我们保留随时修改本协议的权利。修改后的协议将在咖啡札记上公布，并自公布之日起生效。您继续使用咖啡札记服务将视为您接受修改后的协议。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("7. 适用法律与争议解决")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("7.1 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。\n7.2 如双方就本协议内容或执行发生争议，应友好协商解决；协商不成时，任何一方均可向有管辖权的人民法院提起诉讼。")
                            .padding(.bottom, 8)
                    }
                    
                    Group {
                        Text("8. 联系我们")
                            .font(.headline)
                            .padding(.top, 8)
                        
                        Text("如您对本协议或咖啡札记服务有任何问题，请通过以下方式联系我们：\n电子邮件：<EMAIL>")
                            .padding(.bottom, 16)
                    }
                }
                .padding()
                .background(GeometryReader { geometry in
                    Color.clear.preference(key: ScrollOffsetPreferenceKey.self, 
                                          value: geometry.frame(in: .named("scroll")).origin.y)
                })
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = value
                
                // 当滚动超过一定距离时，逐渐改变标题透明度
                let threshold: CGFloat = -50
                if value < threshold {
                    let progress = min(1.0, (abs(value) - abs(threshold)) / 30)
                    titleOpacity = 1.0 - progress
                    navTitleOpacity = progress
                } else {
                    titleOpacity = 1.0
                    navTitleOpacity = 0.0
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .presentationDragIndicator(.visible)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("用户协议")
                        .font(.headline)
                        .opacity(navTitleOpacity)
                }
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Text("关闭")
                            .foregroundColor(.linkText)
                    }
                }
            }
        }
    }
}

#Preview {
    UserAgreementView()
} 