import SwiftUI

struct OnboardingView: View {
    @Environment(\.dismiss) var dismiss
    @State private var selectedPage = 0

    // 添加完成回调
    let onComplete: (() -> Void)?

    // 添加初始化方法
    init(onComplete: (() -> Void)? = nil) {
        self.onComplete = onComplete
    }
    
    let pages = [
        OnboardingPage(
            title: "记录每一杯咖啡",
            description: "记录咖啡冲煮过程中的各项参数，包括咖啡豆、器具、研磨度、水温等，帮助你追踪和改进冲煮技巧。",
            imageName: "cup.and.saucer.fill"
        ),
        OnboardingPage(
            title: "管理咖啡器具",
            description: "记录你的咖啡器具信息，包括购买日期、使用笔记等，帮助你更好地维护和使用器具。",
            imageName: "gearshape.2.fill"
        ),
        OnboardingPage(
            title: "追踪咖啡豆",
            description: "记录咖啡豆的品种、烘焙日期、风味特点等信息，帮助你发现和记住喜欢的咖啡豆。",
            imageName: "leaf.fill"
        ),
        OnboardingPage(
            title: "数据分析",
            description: "通过热力图、统计图表等方式，直观地了解你的咖啡冲煮习惯和进步。",
            imageName: "chart.bar.fill"
        )
    ]
    
    var body: some View {
        TabView(selection: $selectedPage) {
            ForEach(pages.indices, id: \.self) { index in
                OnboardingPageView(page: pages[index])
            }
        }
        .tabViewStyle(.page)
        .indexViewStyle(.page(backgroundDisplayMode: .always))
        .overlay(alignment: .topTrailing) {
            if selectedPage < pages.count - 1 {
                Button("跳过") {
                    dismiss()
                    onComplete?()
                }
                .padding()
            }
        }
        .overlay(alignment: .bottom) {
            if selectedPage == pages.count - 1 {
                Button(action: {
                    dismiss()
                }) {
                    Text("开始使用")
                        .font(.headline)
                        .foregroundColor(.primaryBg)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.accentColor)
                        .cornerRadius(10)
                }
                .padding()
            }
        }
    }
}

struct OnboardingPage {
    let title: String
    let description: String
    let imageName: String
}

struct OnboardingPageView: View {
    let page: OnboardingPage
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: page.imageName)
                .font(.system(size: 80))
                .foregroundColor(.accentColor)
            
            Text(page.title)
                .font(.title)
                .bold()
            
            Text(page.description)
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 32)
            
            Spacer()
            Spacer()
        }
    }
} 
