import SwiftUI
import UIKit

/// 应用的开屏界面
struct SplashScreenView: View {
    var body: some View {
        ZStack {
            // 背景色
            Color.primaryBg
                .ignoresSafeArea()

            // 使用SVG图标居中显示
            Image("brewlog-chn-logo")
                .renderingMode(.template) // 先设置renderingMode
                .resizable()
                .scaledToFit()
                .frame(height: 30)
                .foregroundColor(.functionText)
        }
    }
}

/// 主题切换过渡界面
struct ThemeTransitionView: View {
    @Binding var isShowing: Bool
    let onComplete: () -> Void

    @State private var opacity: Double = 0

    var body: some View {
        SplashScreenView()
            .opacity(opacity)
            .onAppear {
                // 在显示时执行淡入动画
                withAnimation(.easeIn(duration: 0.2)) {
                    opacity = 1
                }

                // 淡入后短暂停留，然后淡出
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    withAnimation(.easeOut(duration: 0.3)) {
                        opacity = 0
                    }

                    // 动画结束后调用完成回调
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        isShowing = false
                        onComplete()
                    }
                }
            }
    }
}

/// 修改后的App启动流程，加入开屏界面
struct SplashScreenContainer: View {
    @State private var isShowingSplash = true
    @State private var isShowingThemeTransition = false
    @EnvironmentObject private var authService: AuthService
    @EnvironmentObject private var appState: AppState
    @EnvironmentObject private var subscriptionService: SubscriptionService
    @EnvironmentObject private var themeManager: ThemeManager
    @EnvironmentObject private var networkMonitor: NetworkMonitor

    var body: some View {
        ZStack {
            if isShowingSplash {
                SplashScreenView()
                    .onAppear {
                        // 预加载键盘资源以避免首次使用输入框时的延迟
                        // 使用新的KeyboardUtils工具类
                        // KeyboardUtils.shared.preloadKeyboard() // 临时注释掉，可能导致启动卡死

                        // 显示开屏界面2秒后切换到主界面
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            withAnimation {
                                isShowingSplash = false
                            }
                        }
                    }
            } else {
                // 主界面
                ZStack {
                    if authService.isAuthenticated {
                        ZStack {
                            MainTabView()
                                .environmentObject(authService)
                                .environmentObject(appState)
                                .environmentObject(subscriptionService)
                                .environmentObject(themeManager)
                                .environmentObject(networkMonitor)
                                .preferredColorScheme(colorSchemeFromThemeMode(themeManager.themeMode))
                                .onChange(of: themeManager.isChangingTheme) { isChanging in
                                    if isChanging {
                                        isShowingThemeTransition = true
                                    }
                                }
                                // 监听主题变化通知
                                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
                                    isShowingThemeTransition = true
                                }

                            // 添加主题切换过渡效果
                            if isShowingThemeTransition {
                                ThemeTransitionView(isShowing: $isShowingThemeTransition) {
                                    // 过渡完成后的回调
                                }
                            }
                        }
                    } else {
                        // 未登录时显示登录界面（无需额外的动画，因为LoginView内部已实现）
                        LoginView(onLoginSuccess: {
                            // 登录成功后不需要额外操作，因为 authService.isAuthenticated 会自动更新
                        })
                        .environmentObject(authService)
                        .environmentObject(subscriptionService)
                        .environmentObject(themeManager)
                        .environmentObject(networkMonitor)
                        .preferredColorScheme(colorSchemeFromThemeMode(themeManager.themeMode))
                        // 监听主题变化通知
                        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ThemeDidChange"))) { _ in
                            isShowingThemeTransition = true
                        }
                    }
                }
            }
        }
    }

    // 根据主题模式返回合适的 ColorScheme
    private func colorSchemeFromThemeMode(_ mode: ThemeMode) -> ColorScheme? {
        switch mode {
        case .system:
            return nil // nil 表示跟随系统
        case .light:
            return .light
        case .dark:
            return .dark
        }
    }
}

#Preview {
    SplashScreenView()
        .environmentObject(ThemeManager.shared)
}
