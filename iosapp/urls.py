from django.urls import path, include
from . import views
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.routers import DefaultRouter
from django.views.decorators.csrf import csrf_exempt

app_name = 'ios_api'

# 创建路由器处理equipment端点
@api_view(['GET', 'POST'])
@csrf_exempt  # 添加CSRF豁免
def equipment_router(request):
    """
    路由器函数，根据请求方法将请求分发到相应的处理函数
    注意：这里直接访问request._request获取原始的HttpRequest对象
    """
    # 获取原始的Django HttpRequest对象
    http_request = request._request

    if request.method == 'GET':
        return views.equipment_list(http_request)
    elif request.method == 'POST':
        return views.create_equipment(http_request)

# 处理冲煮记录的路由函数
@api_view(['GET', 'POST'])
@csrf_exempt  # 添加CSRF豁免
def brewlog_records_router(request):
    """
    路由函数，处理冲煮记录的GET和POST请求
    """
    http_request = request._request

    if request.method == 'GET':
        return views.brewlog_list(http_request)
    elif request.method == 'POST':
        return views.create_brewlog(http_request)

# 处理单个冲煮记录的路由函数
@api_view(['GET', 'PUT', 'DELETE'])
@csrf_exempt  # 添加CSRF豁免
def brewlog_record_detail_router(request, record_id):
    """
    路由函数，处理单个冲煮记录的GET、PUT和DELETE请求
    """
    http_request = request._request

    if request.method == 'GET':
        return views.get_brewlog(http_request, record_id)
    elif request.method == 'PUT':
        return views.update_brewlog(http_request, record_id)
    elif request.method == 'DELETE':
        return views.delete_brewlog(http_request, record_id)

urlpatterns = [
    path('test/', views.api_test, name='api_test'),
    path('csrf-token/', views.get_csrf_token, name='get_csrf_token'),
    path('auth/login/', views.api_login, name='login'),
    path('auth/register/', views.api_register, name='register'),
    path('auth/password/reset/', views.api_password_reset, name='password_reset'),
    path('auth/check-username/', views.check_username_availability, name='check_username_availability'),
    path('auth/token/refresh/', views.token_refresh, name='token_refresh'),
    path('auth/token/verify/', views.token_verify, name='token_verify'),
    path('auth/token/blacklist/', views.token_blacklist, name='token_blacklist'),
    path('user/profile/', views.user_profile, name='user_profile'),
    path('user/devices/', views.user_devices, name='user_devices'),
    path('user/device/blacklist/', views.blacklist_device, name='blacklist_device'),
    path('user/device/blacklist/<str:device_id>/', views.blacklist_device, name='blacklist_device_with_id'),
    path('brewlog/', views.brewlog_list, name='brewlog_list'),
    path('brewlog/filtered-records/', views.filtered_brewlog_list, name='filtered_brewlog_list'),
    path('brewlog/delete/<int:record_id>/', views.delete_brewlog, name='delete_brewlog'),
    path('brewlog/statistics/', views.brewlog_statistics, name='brewlog_statistics'),
    path('brewlog/brew-methods/', views.brew_methods_list, name='brew_methods_list'),
    # 新增：处理brewlog/records/的路由
    path('brewlog/records/', brewlog_records_router, name='brewlog_records'),
    path('brewlog/records/<int:record_id>/', brewlog_record_detail_router, name='brewlog_record_detail'),
    # 使用路由器函数处理设备API
    path('equipment/', equipment_router, name='equipment_router'),
    path('equipment/<int:equipment_id>/', views.equipment_detail, name='equipment_detail'),
    path('equipment/<int:equipment_id>/toggle_favorite/', views.toggle_favorite_equipment, name='toggle_favorite_equipment'),
    path('equipment/<int:equipment_id>/archive/', views.archive_equipment, name='archive_equipment'),
    path('equipment/<int:equipment_id>/unarchive/', views.unarchive_equipment, name='unarchive_equipment'),
    path('equipment/<int:equipment_id>/update/', views.update_equipment, name='update_equipment'),
    path('beans/', views.bean_list, name='bean_list'),
    path('beans/create/', views.create_bean, name='create_bean'),
    path('beans/<int:bean_id>/', views.bean_detail, name='bean_detail'),
    path('beans/<int:bean_id>/toggle_favorite/', views.toggle_favorite_bean, name='toggle_favorite_bean'),
    path('beans/<int:bean_id>/archive/', views.archive_bean, name='archive_bean'),
    path('beans/<int:bean_id>/unarchive/', views.unarchive_bean, name='unarchive_bean'),
    path('beans/<int:bean_id>/occurrence/', views.create_bean_occurrence, name='create_bean_occurrence'),
    path('beans/occurrence/<int:occurrence_id>/', views.update_bean_occurrence, name='update_bean_occurrence'),
    path('beans/occurrence/<int:occurrence_id>/delete/', views.delete_bean_occurrence, name='delete_bean_occurrence'),
    path('beans/calendar/', views.bean_calendar_data, name='bean_calendar_data'),
    path('data/version/', views.data_version, name='data_version'),
    path('brewlog/hindsight/', views.hindsight_data, name='hindsight_data'),
    path('brewlog/heatmap/', views.heatmap_data, name='heatmap_data'),
    path('brewlog/available-years/', views.available_years, name='available_years'),
    path('flavor-tags/', views.flavor_tags_list, name='flavor_tags_list'),
    path('flavor-tags/create/', views.create_flavor_tag, name='create_flavor_tag'),
    path('equipment/gadgets/', views.gadget_list, name='gadget_list'),
    # 配方相关API
    path('recipes/', views.recipe_list, name='recipe_list'),
    path('recipes/tags/', views.recipe_tags_list, name='recipe_tags_list'),
    path('recipes/tags/<int:tag_id>/', views.recipe_tag_detail, name='recipe_tag_detail'),
    path('recipes/<int:recipe_id>/quick-brew/', views.quick_brew, name='quick_brew'),
    path('recipes/<int:recipe_id>/preview/', views.preview_quick_brew, name='preview_quick_brew'),
    path('recipes/<int:recipe_id>/rename/', views.rename_recipe, name='rename_recipe'),
    path('recipes/<int:recipe_id>/tags/', views.update_recipe_tags, name='update_recipe_tags'),
    # 后续可以添加更多iOS专用的API
]