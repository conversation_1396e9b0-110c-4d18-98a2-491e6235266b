# Generated migration for ensuring username uniqueness and performance

from django.db import migrations, models
from django.contrib.auth.models import User

class Migration(migrations.Migration):

    dependencies = [
        ('iosapp', '0002_iosdevice_app_version_iosdevice_blacklisted_at_and_more'),
    ]

    # 禁用原子性，允许CREATE INDEX CONCURRENTLY
    atomic = False

    operations = [
        # 确保用户名字段有唯一索引（Django默认User模型已有，但为了确保性能添加额外索引）
        migrations.RunSQL(
            # 创建用户名的大小写不敏感索引（用于快速查找）
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS auth_user_username_lower_idx ON auth_user (LOWER(username));",
            # 回滚时删除索引
            "DROP INDEX IF EXISTS auth_user_username_lower_idx;",
        ),

        # 注意：暂时不添加约束检查，因为现有数据可能不符合新的用户名规则
        # 新用户注册时会在应用层进行验证
    ]
